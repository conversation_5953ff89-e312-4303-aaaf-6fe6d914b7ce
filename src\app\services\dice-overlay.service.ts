import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface DiceModifier {
  label: string;
  value: number;
}

export interface DiceResult {
  roll: number;
  dc: number;
  success: boolean;
  total?: number; // roll + modifiers
}

export interface DiceOverlayState {
  isVisible: boolean;
  result: DiceResult | null;
  dc: number;
  modifiers: DiceModifier[];
}

@Injectable({
  providedIn: 'root'
})
export class DiceOverlayService {
  private overlayState = new BehaviorSubject<DiceOverlayState>({
    isVisible: false,
    result: null,
    dc: 10,
    modifiers: []
  });

  public overlayState$ = this.overlayState.asObservable();

  constructor() { }

  /**
   * Show dice roll overlay (BG3 style - user clicks dice to roll)
   * @param dc The difficulty class
   * @param modifiers Array of modifiers to apply to the roll
   */
  showDiceRoll(dc: number, modifiers: DiceModifier[] = []): void {
    console.log('DiceOverlayService: Showing dice roll overlay:', { dc, modifiers });

    // Show overlay without result initially (user will click to roll)
    // Always start with a clean state (no previous result)
    this.overlayState.next({
      isVisible: true,
      result: null,
      dc: dc,
      modifiers: modifiers
    });
  }

  /**
   * Execute the dice roll and update the overlay with results
   */
  executeDiceRoll(): void {
    const currentState = this.overlayState.value;
    if (!currentState.isVisible) return;

    // Roll the dice
    const roll = Math.floor(Math.random() * 20) + 1;

    // Calculate total with modifiers
    const modifierTotal = currentState.modifiers.reduce((sum, mod) => sum + mod.value, 0);
    const total = roll + modifierTotal;

    // Check success
    const success = total >= currentState.dc;

    const result: DiceResult = {
      roll,
      dc: currentState.dc,
      success,
      total
    };

    console.log('DiceOverlayService: Dice roll executed:', result);

    // Update overlay with result
    this.overlayState.next({
      ...currentState,
      result: result
    });
  }

  /**
   * Legacy method for backward compatibility
   * Show dice result overlay
   * @param roll The dice roll result (1-20)
   * @param dc The difficulty class
   * @param success Whether the roll was successful
   * @param waitForClick Whether to wait for user click to dismiss (default: true)
   */
  showDiceResult(roll: number, dc: number, success: boolean, waitForClick: boolean = true): void {
    console.log('DiceOverlayService: Showing dice result (legacy):', { roll, dc, success, waitForClick });

    const result: DiceResult = { roll, dc, success, total: roll };

    // Show overlay with immediate result
    this.overlayState.next({
      isVisible: true,
      result: result,
      dc: dc,
      modifiers: []
    });

    // If not waiting for click, auto-hide after duration (legacy behavior)
    if (!waitForClick) {
      setTimeout(() => {
        this.hideOverlay();
      }, 2000);
    }
    // Otherwise, overlay stays visible until user clicks or hideOverlay() is called
  }

  /**
   * Hide the dice overlay
   */
  hideOverlay(): void {
    console.log('DiceOverlayService: Hiding dice overlay');

    const currentState = this.overlayState.value;

    // First emit with result still present so dialogue can process it
    this.overlayState.next({
      isVisible: false,
      result: currentState.result, // Keep result for dialogue processing
      dc: currentState.dc,
      modifiers: currentState.modifiers
    });

    // Then immediately clear the result to prevent interference with next roll
    // Use setTimeout to ensure the first emission is processed first
    setTimeout(() => {
      this.overlayState.next({
        isVisible: false,
        result: null, // Clear the result after dialogue has processed it
        dc: currentState.dc,
        modifiers: currentState.modifiers
      });
    }, 0);
  }

  /**
   * Get current overlay state (for components that need immediate access)
   */
  getCurrentState(): DiceOverlayState {
    return this.overlayState.value;
  }

  /**
   * Check if overlay is currently visible
   */
  isVisible(): boolean {
    return this.overlayState.value.isVisible;
  }

  /**
   * Reset the overlay state completely (for starting fresh)
   */
  resetOverlay(): void {
    console.log('DiceOverlayService: Resetting dice overlay');

    this.overlayState.next({
      isVisible: false,
      result: null,
      dc: 10,
      modifiers: []
    });
  }
}
