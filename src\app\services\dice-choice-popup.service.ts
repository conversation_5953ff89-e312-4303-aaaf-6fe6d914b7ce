import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export enum DiceChoiceOption {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  ROLL_DICE = 'roll_dice'
}

export interface DiceChoiceState {
  isVisible: boolean;
  option?: any; // The option that triggered the choice
  onChoice?: (choice: DiceChoiceOption) => void; // Callback for when user makes a choice
}

@Injectable({
  providedIn: 'root'
})
export class DiceChoicePopupService {
  private choiceState = new BehaviorSubject<DiceChoiceState>({
    isVisible: false
  });

  public choiceState$ = this.choiceState.asObservable();

  constructor() { }

  /**
   * Show the dice choice popup
   * @param option The option that triggered the choice
   * @param onChoice Callback function to handle the user's choice
   */
  showChoicePopup(option: any, onChoice: (choice: DiceChoiceOption) => void): void {
    console.log('DiceChoicePopupService: Showing choice popup for option:', option);

    this.choiceState.next({
      isVisible: true,
      option: option,
      onChoice: onChoice
    });
  }

  /**
   * Hide the choice popup
   */
  hideChoicePopup(): void {
    console.log('DiceChoicePopupService: Hiding choice popup');
    
    this.choiceState.next({
      isVisible: false,
      option: undefined,
      onChoice: undefined
    });
  }

  /**
   * Handle user choice and execute callback
   * @param choice The user's choice
   */
  makeChoice(choice: DiceChoiceOption): void {
    const currentState = this.choiceState.value;
    
    console.log('DiceChoicePopupService: User made choice:', choice);
    
    if (currentState.onChoice) {
      currentState.onChoice(choice);
    }
    
    // Hide the popup after choice is made
    this.hideChoicePopup();
  }

  /**
   * Get current choice state (for components that need immediate access)
   */
  getCurrentState(): DiceChoiceState {
    return this.choiceState.value;
  }

  /**
   * Check if choice popup is currently visible
   */
  isVisible(): boolean {
    return this.choiceState.value.isVisible;
  }
}
