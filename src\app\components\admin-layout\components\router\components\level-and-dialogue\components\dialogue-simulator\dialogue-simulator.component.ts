import { Component, OnInit, Input, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { DilemmaBoxService } from 'src/app/services';
import { Dialogue, StoryBox, OptionBox, DilemmaBox} from 'src/app/lib/@bus-tier/models';
import { RoadBlockService } from 'src/app/services/road-block.service';


@Component({
  selector: 'dialogue-simulator',
  templateUrl: './dialogue-simulator.component.html',
  styleUrls: ['dialogue-simulator.component.scss']
})
/**
 * Displays and edits the dialogue from a level
 */
export class DialogueSimulatorComponent
  implements OnInit
{
  @ViewChild('messages') private myScrollContainer: ElementRef;
  messageTyppingDelay = 600; //ms
  messagesBetweenDelay = 1000; //ms

  @Input() public dialogue: Dialogue;
  public allBoxes: (StoryBox | OptionBox | DilemmaBox)[] = [];
  public loadedBoxes: (StoryBox | OptionBox | DilemmaBox)[] = [];
  public finishedSimulation = false;
  public isRoadBlockViewEnabled = true;
  public isDiceSimulationEnabled = true;

  constructor(
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _change: ChangeDetectorRef,
    private _roadBlockService: RoadBlockService,
  ) {
  }

  public ngOnInit(): void
  {
    if(this.dialogue.boxIds.length > 0)
    {
      this.dialogue.boxIds.forEach(boxId => {
        let storyBox = this._storyBoxService.svcFindById(boxId);
        let optionBox = this._optionBoxService.svcFindById(boxId);
        let dilemmabox = this._dilemmaBoxService.svcFindById(boxId);
        if (storyBox)
        {
          this.allBoxes.push(storyBox);
        }
        else if (optionBox)
        {
          this.allBoxes.push(optionBox);
        }
        else if (dilemmabox)
        {
          this.allBoxes.push(dilemmabox);
        }
      });
    }
    this.processNextBox();
    console.log('LoadedBoxes', this.loadedBoxes);
  }

  public processNextBox()
  {
    if(this.allBoxes.length > this.loadedBoxes.length)
    {
      this.loadedBoxes.push(this.allBoxes[this.loadedBoxes.length]);
    }
    else
    {
      this.finishedSimulation = true;
    }
    this.scrollToBottom();
    this._change.detectChanges();
  }

  toggleRoadBlockView() {
  this.isRoadBlockViewEnabled = !this.isRoadBlockViewEnabled;
  }

  toggleDiceSimulation() {
    this.isDiceSimulationEnabled = !this.isDiceSimulationEnabled;
  }

  public updateScroll()
  {
    this.scrollToBottom();
    this._change.detectChanges();
  }

  public changeMessageSpeed(value)
  {
    value = 3000 - value;
    this.messagesBetweenDelay = Math.round(value * 1);
    this.messageTyppingDelay = Math.round(value * 0.6);
  }

  public updateSliderProgress(event: any) {
    // Update slider progress for the green trail effect
    const progress = ((event.target.value - event.target.min) / (event.target.max - event.target.min)) * 100;
    event.target.style.setProperty('--slider-progress', `${progress}%`);
  }

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  public scrollToBottom(): void {
    try {
        this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    } catch(err) { }
  }

}
