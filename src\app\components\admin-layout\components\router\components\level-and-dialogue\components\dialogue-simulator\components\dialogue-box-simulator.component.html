<!-- https://codepen.io/supah/pen/jqOBqp -->

<!-- Container with roadblock bracket system -->
<div class="dialogue-box-container" [class.has-roadblock]="hasRoadBlock && isRoadBlockViewEnabled">

  <!-- Roadblock bracket (only shows when roadblocks are present and enabled) -->
  <div class="roadblock-bracket" *ngIf="hasRoadBlock && isRoadBlockViewEnabled">
    <div class="roadblock-bracket-line roadblock-bracket-top"></div>
    <div class="roadblock-bracket-line roadblock-bracket-middle">
      <i class="pe-7s-lock roadblock-lock-icon"></i>
    </div>
    <div class="roadblock-bracket-line roadblock-bracket-bottom"></div>
  </div>

  <!-- Content area -->
  <div class="dialogue-content">
    <table class="tableOptions">
      <tr *ngFor="let option of options">
        <td>
          <div
            style="margin-bottom: 5px;"
            class="message option clickable new right"
            [class.disabled]="sessionService.usedOptionsPerBox.get(box.id)?.has(option.id)"
            [ngClass]="{'selected' : this.selectedOption !== undefined}"
            (click)="!sessionService.usedOptionsPerBox.get(box.id)?.has(option.id) &&  processedOption(option.id)">
              <div
                class="material-icons"
                [ngClass]="optionType === 1 ? 'sms-icon' : 'announcement-icon'">
                {{ optionType === 1 ? 'sms' : 'announcement' }}
              </div>
              <div style="display: inline" [innerHTML]="option.message | rpgFormatting"></div>
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <div class="message option clickable new right"
            *ngIf="options?.length > 0 && this.optionType === 1 && this.selectedOption === undefined"
            (click)="finishBoxProcessing(true)"
            [ngClass]="{'selected' : this.options?.length == 0}">
            <div class="material-icons">power_settings_new</div>
            <div style="display: inline-block" [innerHTML]="'Exit' | rpgFormatting"></div>
          </div>
        </td>
      </tr>
    </table>

    <table class="tableOptions" *ngIf="dilemmas?.length > 0">
      <tr *ngFor="let dilemma of dilemmas">
        <td>
          <div
            style="margin-bottom: 5px;"
            class="message option clickable new right"
            [ngClass]="{'selected' : selectedOption !== undefined}"
            (click)="processedOption(dilemma.id)">
              <img src="assets/img/icon_dilemma.png" alt="icon" class="icon-png" />
              <div style="display: inline" [innerHTML]="dilemma.message | rpgFormatting"></div>
          </div>
        </td>
      </tr>
    </table>

    <div *ngFor="let message of messages">
      <div *ngIf="message.loading; else messageContent"
        class="message loading new"
        [ngClass]="{'left' : message.sideLeft, 'right' : !message.sideLeft}">
        <span class="b1"></span>
        <span class="b2"></span>
        <span class="b3"></span>
      </div>
      <ng-template #messageContent>
        <div class="message new"
          [ngClass]="{'left' : message.sideLeft, 'right' : !message.sideLeft}">
          <span
            *ngIf="message.name !== undefined"
            [ngStyle]="{'color': message.color? message.color : '#fff'}">{{ message.name }}</span>
          <div [innerHTML]="message.message | rpgFormatting"></div>
        </div>
      </ng-template>
    </div>
  </div>
</div>

<dialogue-box-simulator
  *ngIf="questionAgainBox !== undefined"
  [roadBlockViewEnabled]="isRoadBlockViewEnabled"
  [dialogue]="dialogue"
  [box]="questionAgainBox"
  [messageTyppingDelay]="messageTyppingDelay"
  [messagesBetweenDelay]="messagesBetweenDelay"
  (proccessFinished)="proccessFinished.emit();"
  (updateScroll)="updateUI();"></dialogue-box-simulator>
