<!-- Dice Choice Popup Overlay -->
<div class="dice-choice-overlay" *ngIf="choiceState.isVisible">
  <div class="dice-choice-content">
    <div class="dice-choice-header">
      <div class="dice-choice-icon">🎲</div>
      <h3 class="dice-choice-title">Choose Your Approach</h3>
      <p class="dice-choice-subtitle">How would you like to handle this situation?</p>
    </div>
    
    <div class="dice-choice-buttons">
      <button 
        class="dice-choice-button positive"
        (click)="onChoiceSelected(DiceChoiceOption.POSITIVE)">
        <div class="button-icon">✓</div>
        <div class="button-text">
          <div class="button-title">Positive</div>
          <div class="button-description">Guaranteed success</div>
        </div>
      </button>
      
      <button 
        class="dice-choice-button negative"
        (click)="onChoiceSelected(DiceChoiceOption.NEGATIVE)">
        <div class="button-icon">✗</div>
        <div class="button-text">
          <div class="button-title">Negative</div>
          <div class="button-description">Guaranteed failure</div>
        </div>
      </button>
      
      <button 
        class="dice-choice-button roll-dice"
        (click)="onChoiceSelected(DiceChoiceOption.ROLL_DICE)">
        <div class="button-icon">🎲</div>
        <div class="button-text">
          <div class="button-title">Roll the Dice</div>
          <div class="button-description">Let fate decide</div>
        </div>
      </button>
    </div>
  </div>
</div>
