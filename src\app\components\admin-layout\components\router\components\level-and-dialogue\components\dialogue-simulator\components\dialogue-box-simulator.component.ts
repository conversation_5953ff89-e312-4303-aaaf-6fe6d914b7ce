import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { SpeechService } from 'src/app/services/speech.service';
import { OptionService } from 'src/app/services/option.service';
import { Dialogue, StoryBox, OptionBox, Option, Speech, DilemmaBox, Dilemma} from 'src/app/lib/@bus-tier/models';
import { CharacterService } from 'src/app/services/character.service';
import { OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { characterTypeColor } from 'src/lib/darkcloud/dialogue-system/game-types';
import { AnswerDilemmaBoxService } from 'src/app/services';
import { DilemmaService } from 'src/app/services';
import { AnswerDilemmaBox } from 'src/app/lib/@bus-tier/models/AnswerDilemmaBox';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { DiceOverlayService } from 'src/app/services/dice-overlay.service';
import { DiceChoicePopupService, DiceChoiceOption } from 'src/app/services/dice-choice-popup.service';


interface Message
{
  message: string,
  name?: string,
  color?: string,
  loading: boolean,
  sideLeft: boolean,
  option?: boolean,
}

@Component({
  selector: 'dialogue-box-simulator',
  templateUrl: './dialogue-box-simulator.component.html',
  styleUrls: ['dialogue-box-simulator.component.scss'],
})
/**
 * Displays and edits the dialogue from a level
 */
export class DialogueBoxSimulatorComponent
  implements OnInit, OnDestroy
{
  @Output() proccessFinished: EventEmitter<void> = new EventEmitter();
  @Output() updateScroll: EventEmitter<void> = new EventEmitter();

  @Input() public dialogue: Dialogue;
  @Input() public box: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox;
  @Input() public questionAgainBox: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox;

  public messages: Message[] = [];
  @Input() messageTyppingDelay = 600; //ms
  @Input() messagesBetweenDelay = 1000; //ms

  public options: Option[] = [];
  public dilemmas: Dilemma[] = [];
  public optionType = undefined;
  public selectedOption: string;
  public usedOptionsPerBox: Map<string, Set<string>> = new Map();


  timeout:any;
  timeout2:any;

  public hasRoadBlock: boolean = false;
  public isRoadBlockViewEnabled = false;
  public isDiceSimulationEnabled = true;
  private waitingForDiceResult: boolean = false;
  private activeDiceSubscription: any = null;

  constructor(
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _dilemmaService: DilemmaService,
    private _speechService: SpeechService,
    private _characterService: CharacterService,
    private _change: ChangeDetectorRef,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    public sessionService: OptionBoxService,
    private _roadBlockService: RoadBlockService,
    private _diceOverlayService: DiceOverlayService,
    private _diceChoicePopupService: DiceChoicePopupService,
  ) {}

  public ngOnInit(): void
  {
    this.processBox(this.box);
    console.log('messages', this.messages);

    
  }

  private processBox(box: StoryBox | OptionBox | DilemmaBox | AnswerDilemmaBox) {
    this.messages = [];
    console.log("processBox", box )
    if (box instanceof StoryBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.storyProgressIds?.length > 0) {
        this.processNextStoryProgress(box.storyProgressIds);
      }

      else {
        this.finishBoxProcessing();
      }
    }
    else if (box instanceof OptionBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.optionIds?.length > 0) {
        this.optionType = box.type;
        this.processOptions(box.optionIds);
      }

      else {
        this.finishBoxProcessing();
      }
    }
    else if (box instanceof DilemmaBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.optionDilemmaIds?.length > 0) {
        console.log('DilemmaBox ID:' ,box.optionDilemmaIds);
        this.processDilemmas(box.optionDilemmaIds);

      }

      else {
        this.finishBoxProcessing();
      }
    }

    else if (box instanceof AnswerDilemmaBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(box.id);
      if (box.storyProgressIds?.length > 0) {
        this.processNextStoryProgress(box.storyProgressIds);
      }

       else {
        this.finishBoxProcessing();
      }
    }

    else {
      this.finishBoxProcessing();
    }
  }

  processNextStoryProgress(storyProgressIds: string[], current = 0)
  {
    if(current < storyProgressIds.length)
    {
      let delay = 1;
      let speech = this._speechService.svcFindById(storyProgressIds[current]);

      if(speech)
      {
        this.startTyppingAnimation(this._characterService.svcFindById(speech.speakerId)?.id != 'C0');
        delay = this.messageTyppingDelay
      this.timeout =  setTimeout(() => {
          this.finsishTyppingAnimation();
          this.addSpeechMessage(speech);
          this.updateUI();
        }, delay)
        delay = this.messagesBetweenDelay + this.messageTyppingDelay
      }

     this.timeout2 = setTimeout(() => {
        this.processNextStoryProgress(storyProgressIds, current + 1);
        this.updateUI();
      }, delay)
    }
    else
    {
      this.finishBoxProcessing();
    }
    this.updateUI();
  }

  public updateUI()
  {
    this._change.detectChanges();
    this.updateScroll.emit();
  }

  processOptions(optionIds: string[]) {
    this.options = this._optionService.svcCloneByIds(optionIds);

    if (!this.sessionService.usedOptionsPerBox.has(this.box.id)) {
    this.sessionService.usedOptionsPerBox.set(this.box.id, new Set<string>());
    }
  }

  processDilemmas(dilemmaIds: string[]) {
    this.dilemmas = this._dilemmaService.svcCloneByIds(dilemmaIds);
    console.log('processDilemmas:', this.dilemmas);
  }

  // ===== DICE SYSTEM METHODS =====

  /**
   * Check if an option has dice system (both success and failure outcomes)
   */
  private hasDiceSystem(option: Option): boolean {
    return !!(option.answerBoxId && option.answerBoxNegativeId);
  }



  /**
   * Show dice choice popup for user to decide approach
   */
  private showDiceChoicePopup(option: Option): void {
    this._diceChoicePopupService.showChoicePopup(option, (choice: DiceChoiceOption) => {
      this.handleDiceChoice(option, choice);
    });
  }

  /**
   * Handle the user's choice from the dice choice popup
   */
  private handleDiceChoice(option: Option, choice: DiceChoiceOption): void {
    let targetBoxId: string;

    switch (choice) {
      case DiceChoiceOption.POSITIVE:
        // Guaranteed success - use positive answer box
        targetBoxId = option.answerBoxId;
        console.log('User chose positive outcome');
        this.processTargetBox(targetBoxId);
        break;

      case DiceChoiceOption.NEGATIVE:
        // Guaranteed failure - use negative answer box
        targetBoxId = option.answerBoxNegativeId;
        console.log('User chose negative outcome');
        this.processTargetBox(targetBoxId);
        break;

      case DiceChoiceOption.ROLL_DICE:
        // Show BG3-style dice roll overlay
        console.log('User chose to roll dice');
        this.showBG3DiceRoll(option);
        break;

      default:
        console.error('Unknown dice choice:', choice);
        return;
    }
  }

  /**
   * Show BG3-style dice roll overlay
   */
  private showBG3DiceRoll(option: Option): void {
    const dc = option.resultDC || 10;
    const modifiers = []; // For now, no modifiers in simulator

    console.log('Showing BG3-style dice roll for option:', option, 'DC:', dc);

    // Clean up any existing dice subscription
    this.cleanupDiceSubscription();

    // Set flag to indicate we're waiting for a dice result
    this.waitingForDiceResult = true;

    // Subscribe to overlay state BEFORE showing the overlay to avoid race conditions
    let isProcessed = false; // Flag to prevent multiple processing

    this.activeDiceSubscription = this._diceOverlayService.overlayState$.subscribe(state => {
      console.log('Dialogue simulator received dice overlay state:', {
        isVisible: state.isVisible,
        hasResult: !!state.result,
        waitingForDiceResult: this.waitingForDiceResult,
        isProcessed: isProcessed,
        result: state.result
      });

      if (!state.isVisible && state.result && this.waitingForDiceResult && !isProcessed) {
        // Mark as processed to prevent multiple executions
        isProcessed = true;

        console.log('Processing dice result - overlay dismissed with result');

        // User has dismissed the overlay and we have a result
        const success = state.result.success;
        const targetBoxId = success ? option.answerBoxId : option.answerBoxNegativeId;

        console.log('Dice roll completed:', state.result, 'Target box:', targetBoxId);

        // Clear the waiting flag
        this.waitingForDiceResult = false;

        // Clean up subscription
        this.cleanupDiceSubscription();

        // Process the target box
        this.processTargetBox(targetBoxId);
      }
    });

    // Show the dice roll overlay AFTER setting up the subscription
    this._diceOverlayService.showDiceRoll(dc, modifiers);
  }

  /**
   * Clean up active dice subscription safely
   */
  private cleanupDiceSubscription(): void {
    if (this.activeDiceSubscription && !this.activeDiceSubscription.closed) {
      this.activeDiceSubscription.unsubscribe();
    }
    this.activeDiceSubscription = null;
  }



  /**
   * Process the target box (extracted for reuse)
   */
  private processTargetBox(targetBoxId: string): void {
    const storyBox = this._storyBoxService.svcFindById(targetBoxId);
    const optionBox = this._optionBoxService.svcFindById(targetBoxId);

    if (storyBox) {
      this.processBox(storyBox);
    } else if (optionBox) {
      this.processBox(optionBox);
    }
  }

  public processedOption(id: string)
  {
    if(this.selectedOption) return;
    this.selectedOption = id;

    // Handle investigation options tracking
    if(this.optionType === OptionBoxType.INVESTIGATION){
      const usedOptions = this.sessionService.usedOptionsPerBox.get(this.box.id)!;
      usedOptions.add(id);
    }

    // Filter options and get the selected one
    this.options = this.options.filter(o => o.id == id && !o.isOmitted);
    this.dilemmas = this.dilemmas.filter(x => x.id == id);
    let option = this._optionService.svcFindById(id);
    let dilemma = this._dilemmaService.svcFindById(id);

    if (!option && !dilemma) return;

    // Handle dilemmas (existing logic)
    if (id.includes('DIL')) {
      console.log('Contém DIL');
      let dilemmabox = this._answerDilemmaBoxService.svcFindById(dilemma?.idDilemmaBox);
      console.log('Dilemma',dilemmabox);
      if (dilemmabox) {
        this.processBox(dilemmabox);
      }
      return;
    }

    // Handle regular options with potential dice system
    if (!option) return;

    // Determine target box based on dice system
    let targetBoxId = option.answerBoxId; // Default to success

    // Check if dice system should be applied
    if (this.isDiceSimulationEnabled && this.hasDiceSystem(option)) {
      // Show choice popup for user to decide approach
      this.showDiceChoicePopup(option);
    } else {
      // No dice system or dice disabled - process immediately
      this.processTargetBox(targetBoxId);
    }
  }

  @Input() set roadBlockViewEnabled(value: boolean) {
    this.isRoadBlockViewEnabled = value;
    this.recheckRoadBlock();
    // Trigger change detection to ensure UI updates
    this._change.detectChanges();
  }

  @Input() set diceSimulationEnabled(value: boolean) {
    this.isDiceSimulationEnabled = value;
    this._change.detectChanges();
  }

  private recheckRoadBlock() {
    const currentBox = this.box;

    // Use the same logic as processBox() to determine which box ID to check for roadblocks
    if (currentBox instanceof StoryBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);
    }
    else if (currentBox instanceof OptionBox) {
      // For OptionBox, check roadblocks on the OptionBox itself (parent box)
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);

      // However, if we have options loaded, we need to check their answer boxes too
      // This handles the case where child options have roadblocks on their answer boxes
      if (this.options && this.options.length > 0) {
        let hasChildRoadblocks = false;
        for (const option of this.options) {
          const optionData = this._optionService.svcFindById(option.id);
          if (optionData && optionData.answerBoxId) {
            if (this._roadBlockService.filterByStoryBoxId(optionData.answerBoxId)) {
              hasChildRoadblocks = true;
              break;
            }
          }
        }
        // If either the parent box or any child has roadblocks, show the bracket
        this.hasRoadBlock = this.hasRoadBlock || hasChildRoadblocks;
      }
    }
    else if (currentBox instanceof DilemmaBox) {
      // For DilemmaBox, check roadblocks on the DilemmaBox itself (parent box)
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);

      // However, if we have dilemmas loaded, we need to check their answer boxes too
      // This handles the case where child dilemmas have roadblocks on their answer boxes
      if (this.dilemmas && this.dilemmas.length > 0) {
        let hasChildRoadblocks = false;
        for (const dilemma of this.dilemmas) {
          const dilemmaData = this._dilemmaService.svcFindById(dilemma.id);
          if (dilemmaData && dilemmaData.idDilemmaBox) {
            if (this._roadBlockService.filterByStoryBoxId(dilemmaData.idDilemmaBox)) {
              hasChildRoadblocks = true;
              break;
            }
          }
        }
        // If either the parent box or any child has roadblocks, show the bracket
        this.hasRoadBlock = this.hasRoadBlock || hasChildRoadblocks;
      }
    }
    else if (currentBox instanceof AnswerDilemmaBox) {
      this.hasRoadBlock = !!this._roadBlockService.filterByStoryBoxId(currentBox.id);
    }
    else {
      this.hasRoadBlock = false;
    }
  }

  onDialogueFinished() {
    this.sessionService.clearSession();
  }


  startTyppingAnimation(sideLeft = true) {
    this.messages.push({
      message: '',
      loading: true,
      sideLeft: sideLeft
    });
  }

  finsishTyppingAnimation() {
    if(this.messages[this.messages.length - 1].loading)
      this.messages.pop();
  }

  addSpeechMessage(speech: Speech) {
    let character = this._characterService.svcFindById(speech.speakerId);
    this.messages.push({
      message: speech.message,
      name: this._characterService.svcFindById(speech.speakerId)?.name,
      color: characterTypeColor[character?.type],
      loading: false,
      sideLeft: character?.id != 'C0'
    });
  }

  finishBoxProcessing(forceFinish = false)
  {
    if(forceFinish)
    {
      this.options = [];
      this.proccessFinished.emit();

       this.sessionService.clearSession();
    }
    else
    {
      if(this.optionType === OptionBoxType.INVESTIGATION)
      {
       
        this.questionAgainBox = this.box;

      }
      else
      {
        this.proccessFinished.emit();
      }
    }
  }


  ngOnDestroy() {
    clearInterval(this.timeout)
    clearInterval(this.timeout2)

    // Clean up dice subscription and reset state
    this.cleanupDiceSubscription();
    this.waitingForDiceResult = false;
  }

}

