/* Dice Choice Popup Overlay */
.dice-choice-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99998; // Slightly lower than dice result overlay
  animation: fadeIn 0.3s ease-in;
}

.dice-choice-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(60, 60, 60, 0.95));
  border: 3px solid #888;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  color: white;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.9);
  animation: slideIn 0.5s ease-out;
  min-width: 400px;
  max-width: 500px;
}

.dice-choice-header {
  margin-bottom: 30px;
}

.dice-choice-icon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.dice-choice-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #fff;
}

.dice-choice-subtitle {
  font-size: 16px;
  color: #ccc;
  margin: 0;
}

.dice-choice-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.dice-choice-button {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border: 2px solid transparent;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
  
  &.positive {
    border-color: #4CAF50;
    
    &:hover {
      background: rgba(76, 175, 80, 0.2);
      border-color: #66BB6A;
    }
  }
  
  &.negative {
    border-color: #F44336;
    
    &:hover {
      background: rgba(244, 67, 54, 0.2);
      border-color: #EF5350;
    }
  }
  
  &.roll-dice {
    border-color: #FF9800;
    
    &:hover {
      background: rgba(255, 152, 0, 0.2);
      border-color: #FFB74D;
    }
  }
}

.button-icon {
  font-size: 24px;
  margin-right: 15px;
  min-width: 30px;
}

.button-text {
  text-align: left;
  flex: 1;
}

.button-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 4px;
}

.button-description {
  font-size: 14px;
  color: #ccc;
}



@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to { 
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}
