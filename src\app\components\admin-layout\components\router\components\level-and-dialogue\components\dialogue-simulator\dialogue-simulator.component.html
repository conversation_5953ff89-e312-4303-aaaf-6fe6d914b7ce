
<div class="wrapper">
  <div class="simulator-container">
    <div class="chat-title">
      <div class="header-content">
        <div class="dialogue-info">
          <h1>DIALOGUE SIMULATION</h1>
          <h2>{{ dialogue.id }}</h2>
        </div>

        <div class="controls-section">
          <div class="control-group">
            <label class="control-label">MESSAGE SPEED</label>
            <div class="speed-control">
              <input type="range" [value]="2000" min="100" max="3000" #speedRanger
                     (input)="changeMessageSpeed(speedRanger.value); updateSliderProgress($event)"
                     class="speed-slider"
                     style="--slider-progress: 67%">
              <span class="speed-value">{{ speedRanger.value }}ms</span>
            </div>
          </div>

          <div class="control-group">
            <label class="control-label">ROADBLOCK VIEW:</label>
            <button
              class="roadblock-button"
              (click)="toggleRoadBlockView()"
              [class.active]="isRoadBlockViewEnabled">
              {{ isRoadBlockViewEnabled ? 'Enabled' : 'Disabled' }}
            </button>
          </div>

          <div class="control-group">
            <label class="control-label">DICE SIMULATION:</label>
            <button
              class="dice-button"
              (click)="toggleDiceSimulation()"
              [class.active]="isDiceSimulationEnabled">
              {{ isDiceSimulationEnabled ? 'Enabled' : 'Disabled' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="messages" #messages>
      <div class="messages-content">
        <div *ngFor="let box of loadedBoxes">
          <dialogue-box-simulator
            [roadBlockViewEnabled]="isRoadBlockViewEnabled"
            [diceSimulationEnabled]="isDiceSimulationEnabled"
            [dialogue]="dialogue"
            [box]="box"
            [messageTyppingDelay]="messageTyppingDelay"
            [messagesBetweenDelay]="messagesBetweenDelay"
            (proccessFinished)="processNextBox()"
            (updateScroll)="updateScroll()"></dialogue-box-simulator>
        </div>
        <div class="endMessage" *ngIf="finishedSimulation">End</div>
        <div class="lockChat">.</div>
      </div>
    </div>
    <div class="message-box">
      <textarea type="text" class="message-input" placeholder="Type message..."></textarea>
      <!-- <button type="submit" class="message-submit">Send</button> -->
    </div>
  </div>
</div>

<!-- Global Dice Choice Popup Component -->
<app-dice-choice-popup></app-dice-choice-popup>

<!-- Global Dice Overlay Component -->
<app-dice-overlay></app-dice-overlay>
