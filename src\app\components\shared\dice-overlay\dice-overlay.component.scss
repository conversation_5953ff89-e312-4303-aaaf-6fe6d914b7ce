/* BG3-Style Dice Overlay */
.dice-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(ellipse at center, rgba(20, 10, 40, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: fadeIn 0.5s ease-in;
  cursor: pointer;
}

.dice-frame {
  position: relative;
  background: linear-gradient(145deg, rgba(40, 30, 20, 0.95), rgba(20, 15, 10, 0.98));
  border-radius: 20px;
  padding: 40px;
  min-width: 450px;
  min-height: 500px;
  box-shadow:
    0 0 50px rgba(139, 69, 19, 0.3),
    inset 0 0 30px rgba(139, 69, 19, 0.1);
  animation: slideIn 0.6s ease-out;
}

/* Ornate Frame Border */
.frame-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.frame-corner {
  position: absolute;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #8B4513, #CD853F);
  border-radius: 50%;

  &.top-left { top: -10px; left: -10px; }
  &.top-right { top: -10px; right: -10px; }
  &.bottom-left { bottom: -10px; left: -10px; }
  &.bottom-right { bottom: -10px; right: -10px; }
}

.frame-edge {
  position: absolute;
  background: linear-gradient(90deg, #8B4513, #CD853F, #8B4513);

  &.top, &.bottom {
    height: 3px;
    left: 30px;
    right: 30px;
  }

  &.left, &.right {
    width: 3px;
    top: 30px;
    bottom: 30px;
  }

  &.top { top: 0; }
  &.bottom { bottom: 0; }
  &.left { left: 0; }
  &.right { right: 0; }
}

.dice-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  color: #F5DEB3;
  text-align: center;
}

/* Header Section */
.dice-header {
  margin-bottom: 30px;
}

.difficulty-label {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 2px;
  color: #CD853F;
  margin-bottom: 8px;
  text-transform: uppercase;
}

.dc-number {
  font-size: 48px;
  font-weight: bold;
  color: #F5DEB3;
  text-shadow: 0 0 20px rgba(245, 222, 179, 0.5);
  font-family: 'Cinzel', serif;
}

/* Dice Area */
.dice-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.dice-container {
  perspective: 1500px;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 3D D20 Dice Variables - adapted from CodePen
$containerWidth: 120px;
$containerHeight: $containerWidth;

$faceWidth: $containerWidth * 0.5;
$faceHeight: $faceWidth * 0.86; // Height of equilateral triangle

// D20 geometry angles - from CodePen
$angle: 53deg;
$ringAngle: -11deg;
$sideAngle: 360deg / 5; // 72deg

// Calculated positioning values - from CodePen
$rotateX: -$angle;
$translateZ: $faceWidth * 0.335;
$translateY: -$faceHeight * 0.15;
$translateRingZ: $faceWidth * 0.75;
$translateRingY: $faceHeight * 0.78 + $translateY;
$translateLowerZ: $translateZ;
$translateLowerY: $faceHeight * 0.78 + $translateRingY;

.dice-3d {
  position: relative;
  width: $containerWidth;
  height: $containerHeight;
  transform-style: preserve-3d;
  cursor: pointer;
  transition: transform 0.5s ease-out;

  // Reset face counter for numbering
  counter-reset: face-counter;

  // Default position - from CodePen
  transform: rotateX($rotateX);

  &:hover:not(.rolling) {
    transform: rotateX($rotateX) scale(1.05);
  }

  &.rolling {
    animation: diceRoll3D 2s ease-out;
  }
}



/* D20 Face Geometry and Positioning - adapted from CodePen */
.face {
  $horizontalMargin: -$faceWidth * 0.5;

  position: absolute;
  left: 50%;
  top: 0;
  margin: 0 $horizontalMargin;

  // Create triangular face using CSS borders
  width: 0;
  height: 0;
  border-left: #{$faceWidth * 0.5} solid transparent;
  border-right: #{$faceWidth * 0.5} solid transparent;
  border-bottom: $faceHeight solid rgba(200, 200, 255, 0.9);

  transform-style: preserve-3d;
  backface-visibility: hidden;

  // Face number counter
  counter-increment: face-counter;

  &::before {
    content: counter(face-counter);
    position: absolute;
    top: #{$faceHeight * 0.25};
    left: -$faceWidth;
    width: #{$faceWidth * 2};
    height: $faceHeight;

    color: #fff;
    font-size: #{$faceHeight * 0.5};
    font-weight: bold;
    text-align: center;
    line-height: #{$faceHeight * 0.9};
    text-shadow: 1px 1px 3px #000;
    user-select: none;
    pointer-events: none;
  }

  // Top pentagon ring (faces 1-5) - based on your dev tools adjustment
  &.face-1 { transform: rotateY(0deg) translateZ(25.2px) translateY(-#{$faceHeight * 0.3}) rotateX($topAngle); }
  &.face-2 { transform: rotateY(72deg) translateZ(25.2px) translateY(-#{$faceHeight * 0.3}) rotateX($topAngle); }
  &.face-3 { transform: rotateY(144deg) translateZ(25.2px) translateY(-#{$faceHeight * 0.3}) rotateX($topAngle); }
  &.face-4 { transform: rotateY(216deg) translateZ(25.2px) translateY(-#{$faceHeight * 0.3}) rotateX($topAngle); }
  &.face-5 { transform: rotateY(288deg) translateZ(25.2px) translateY(-#{$faceHeight * 0.3}) rotateX($topAngle); }

  // Upper middle ring (faces 6-10) - calculated from icosahedron geometry
  &.face-6 { transform: rotateY(36deg) translateZ(40.8px) translateY(#{$faceHeight * 0.4}) rotateX($ringAngle); }
  &.face-7 { transform: rotateY(108deg) translateZ(40.8px) translateY(#{$faceHeight * 0.4}) rotateX($ringAngle); }
  &.face-8 { transform: rotateY(180deg) translateZ(40.8px) translateY(#{$faceHeight * 0.4}) rotateX($ringAngle); }
  &.face-9 { transform: rotateY(252deg) translateZ(40.8px) translateY(#{$faceHeight * 0.4}) rotateX($ringAngle); }
  &.face-10 { transform: rotateY(324deg) translateZ(40.8px) translateY(#{$faceHeight * 0.4}) rotateX($ringAngle); }

  // Lower middle ring (faces 11-15) - same distance as upper middle
  &.face-11 { transform: rotateY(0deg) translateZ(40.8px) translateY(#{$faceHeight * 1.1}) rotateX(-$ringAngle); }
  &.face-12 { transform: rotateY(72deg) translateZ(40.8px) translateY(#{$faceHeight * 1.1}) rotateX(-$ringAngle); }
  &.face-13 { transform: rotateY(144deg) translateZ(40.8px) translateY(#{$faceHeight * 1.1}) rotateX(-$ringAngle); }
  &.face-14 { transform: rotateY(216deg) translateZ(40.8px) translateY(#{$faceHeight * 1.1}) rotateX(-$ringAngle); }
  &.face-15 { transform: rotateY(288deg) translateZ(40.8px) translateY(#{$faceHeight * 1.1}) rotateX(-$ringAngle); }

  // Bottom pentagon ring (faces 16-20) - same as top ring
  &.face-16 { transform: rotateY(36deg) translateZ(25.2px) translateY(#{$faceHeight * 1.8}) rotateX(-$topAngle) rotateZ(180deg); }
  &.face-17 { transform: rotateY(108deg) translateZ(25.2px) translateY(#{$faceHeight * 1.8}) rotateX(-$topAngle) rotateZ(180deg); }
  &.face-18 { transform: rotateY(180deg) translateZ(25.2px) translateY(#{$faceHeight * 1.8}) rotateX(-$topAngle) rotateZ(180deg); }
  &.face-19 { transform: rotateY(252deg) translateZ(25.2px) translateY(#{$faceHeight * 1.8}) rotateX(-$topAngle) rotateZ(180deg); }
  &.face-20 { transform: rotateY(324deg) translateZ(25.2px) translateY(#{$faceHeight * 1.8}) rotateX(-$topAngle) rotateZ(180deg); }
}

/* 3D Rolling Animation */
@keyframes diceRoll3D {
  0% {
    transform: rotateX(-15deg) rotateY(15deg) rotateZ(0deg);
  }
  20% {
    transform: rotateX(180deg) rotateY(270deg) rotateZ(90deg) translateX(20px) translateY(-20px);
  }
  40% {
    transform: rotateX(360deg) rotateY(540deg) rotateZ(180deg) translateX(-15px) translateY(25px);
  }
  60% {
    transform: rotateX(540deg) rotateY(810deg) rotateZ(270deg) translateX(30px) translateY(-10px);
  }
  80% {
    transform: rotateX(720deg) rotateY(1080deg) rotateZ(360deg) translateX(-20px) translateY(15px);
  }
  100% {
    transform: rotateX(900deg) rotateY(1350deg) rotateZ(450deg);
  }
}

/* Face Display Logic - Position dice to show specific face */
.dice-3d {
  // Top pentagon ring (faces 1-5) - rotate to show face up
  &[data-face="1"] { transform: rotateX(-$topAngle) rotateY(0deg); }
  &[data-face="2"] { transform: rotateX(-$topAngle) rotateY(-72deg); }
  &[data-face="3"] { transform: rotateX(-$topAngle) rotateY(-144deg); }
  &[data-face="4"] { transform: rotateX(-$topAngle) rotateY(-216deg); }
  &[data-face="5"] { transform: rotateX(-$topAngle) rotateY(-288deg); }

  // Upper middle ring (faces 6-10) - rotate to show face up
  &[data-face="6"] { transform: rotateX(-$ringAngle) rotateY(-36deg); }
  &[data-face="7"] { transform: rotateX(-$ringAngle) rotateY(-108deg); }
  &[data-face="8"] { transform: rotateX(-$ringAngle) rotateY(-180deg); }
  &[data-face="9"] { transform: rotateX(-$ringAngle) rotateY(-252deg); }
  &[data-face="10"] { transform: rotateX(-$ringAngle) rotateY(-324deg); }

  // Lower middle ring (faces 11-15) - rotate to show face up
  &[data-face="11"] { transform: rotateX($ringAngle) rotateY(0deg); }
  &[data-face="12"] { transform: rotateX($ringAngle) rotateY(-72deg); }
  &[data-face="13"] { transform: rotateX($ringAngle) rotateY(-144deg); }
  &[data-face="14"] { transform: rotateX($ringAngle) rotateY(-216deg); }
  &[data-face="15"] { transform: rotateX($ringAngle) rotateY(-288deg); }

  // Bottom pentagon ring (faces 16-20) - rotate to show face up
  &[data-face="16"] { transform: rotateX($topAngle) rotateY(-36deg) rotateZ(180deg); }
  &[data-face="17"] { transform: rotateX($topAngle) rotateY(-108deg) rotateZ(180deg); }
  &[data-face="18"] { transform: rotateX($topAngle) rotateY(-180deg) rotateZ(180deg); }
  &[data-face="19"] { transform: rotateX($topAngle) rotateY(-252deg) rotateZ(180deg); }
  &[data-face="20"] { transform: rotateX($topAngle) rotateY(-324deg) rotateZ(180deg); }
}

/* Dice Label */
.dice-label {
  font-size: 16px;
  color: #CD853F;
  font-style: italic;
  margin-top: 20px;
  text-align: center;
  animation: pulse 2s infinite;

  /* Box styling similar to the reference image */
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #8B4513;
  border-radius: 8px;
  padding: 8px 16px;
  display: inline-block;
  box-shadow:
    0 0 10px rgba(139, 69, 19, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  /* Subtle gradient for depth */
  background: linear-gradient(145deg,
    rgba(0, 0, 0, 0.8),
    rgba(20, 20, 20, 0.9)
  );
}

/* Roll Result Display */
.roll-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  animation: resultSlideIn 0.8s ease-out;
}

.base-roll {
  font-size: 36px;
  font-weight: bold;
  color: #F5DEB3;
  text-shadow: 0 0 15px rgba(245, 222, 179, 0.6);
  padding: 10px 20px;
  border: 2px solid #CD853F;
  border-radius: 10px;
  background: rgba(139, 69, 19, 0.3);
}

.modifiers {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.modifier {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #98FB98;

  .modifier-sign {
    font-weight: bold;
    color: #90EE90;
  }

  .modifier-value {
    font-weight: bold;
    color: #90EE90;
  }

  .modifier-label {
    font-size: 14px;
    color: #DDD;
    font-style: italic;
  }
}

.total-result {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: bold;
  margin-top: 10px;

  .equals {
    color: #CD853F;
  }

  .total {
    color: #F5DEB3;
    text-shadow: 0 0 20px rgba(245, 222, 179, 0.8);
    padding: 8px 16px;
    border: 2px solid #CD853F;
    border-radius: 8px;
    background: rgba(139, 69, 19, 0.4);
  }
}

/* Action Section */
.dice-action {
  margin-top: auto;
  padding-top: 20px;
}

.dice-button {
  background: linear-gradient(145deg, #8B4513, #CD853F);
  border: 2px solid #D2691E;
  border-radius: 12px;
  color: #F5DEB3;
  font-size: 18px;
  font-weight: 600;
  padding: 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.4);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.6);
    background: linear-gradient(145deg, #A0522D, #DEB887);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.result-display {
  text-align: center;
  animation: resultFadeIn 1s ease-out;
}

.outcome {
  font-size: 32px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 15px;
  text-shadow: 0 0 20px currentColor;

  &.success {
    color: #32CD32;
    animation: successGlow 2s ease-in-out infinite alternate;
  }

  &.failure {
    color: #DC143C;
    animation: failureGlow 2s ease-in-out infinite alternate;
  }
}

.continue-prompt {
  font-size: 16px;
  color: #CD853F;
  font-style: italic;
  animation: pulse 2s infinite;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Additional visual enhancements for the 3D dice */
.dice-3d:hover:not(.rolling) .face {
  border-bottom-color: #F0E6FF;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

@keyframes resultSlideIn {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes resultFadeIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes successGlow {
  from { text-shadow: 0 0 20px #32CD32; }
  to { text-shadow: 0 0 30px #32CD32, 0 0 40px #32CD32; }
}

@keyframes failureGlow {
  from { text-shadow: 0 0 20px #DC143C; }
  to { text-shadow: 0 0 30px #DC143C, 0 0 40px #DC143C; }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dice-frame {
    min-width: 350px;
    min-height: 400px;
    padding: 30px;
  }

  .dc-number {
    font-size: 36px;
  }

  // Responsive 3D dice sizing
  $mobileDiceSize: 100px;
  $mobileFaceSize: $mobileDiceSize * 0.5;
  $mobileFaceHeight: $mobileFaceSize * 0.86;

  .dice-3d {
    width: $mobileDiceSize;
    height: $mobileDiceSize;

    .face {
      margin-left: -#{$mobileFaceSize * 0.5};
      margin-top: -#{$mobileFaceHeight * 0.5};
      border-left-width: #{$mobileFaceSize * 0.5};
      border-right-width: #{$mobileFaceSize * 0.5};
      border-bottom-width: $mobileFaceHeight;

      &::before {
        font-size: #{$mobileFaceHeight * 0.3};
        top: #{$mobileFaceHeight * 0.3};
        left: -#{$mobileFaceSize * 0.5};
        width: $mobileFaceSize;
        height: #{$mobileFaceHeight * 0.4};
        line-height: #{$mobileFaceHeight * 0.4};
      }
    }
  }

  .base-roll {
    font-size: 28px;
  }

  .outcome {
    font-size: 24px;
  }
}
