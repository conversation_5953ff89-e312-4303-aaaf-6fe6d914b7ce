import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DiceOverlayService, DiceOverlayState } from '../../../services/dice-overlay.service';

@Component({
  selector: 'app-dice-overlay',
  templateUrl: './dice-overlay.component.html',
  styleUrls: ['./dice-overlay.component.scss']
})
export class DiceOverlayComponent implements OnInit, OnDestroy {
  public overlayState: DiceOverlayState = {
    isVisible: false,
    result: null,
    dc: 10,
    modifiers: []
  };

  // Animation states
  public isRolling: boolean = false;
  public hasRolled: boolean = false;
  public diceTransform: string = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';

  private subscription: Subscription = new Subscription();
  private rollAnimationTimeout?: number;

  constructor(private diceOverlayService: DiceOverlayService) { }

  ngOnInit(): void {
    // Subscribe to overlay state changes
    this.subscription = this.diceOverlayService.overlayState$.subscribe(
      state => {
        const wasVisible = this.overlayState.isVisible;
        console.log('Dice overlay component received state update:', {
          state,
          wasVisible,
          currentHasRolled: this.hasRolled,
          currentIsRolling: this.isRolling
        });

        this.overlayState = state;

        // Reset animation states when overlay becomes visible (new dice roll)
        if (state.isVisible && !wasVisible) {
          console.log('New dice roll started - resetting dice state');
          this.resetDiceState();
        }

        // If we receive a result and we're currently rolling, finish the roll
        if (state.result && this.isRolling) {
          console.log('Received dice result while rolling - finishing roll');
          this.finishDiceRoll();
        }
      }
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.rollAnimationTimeout) {
      clearTimeout(this.rollAnimationTimeout);
    }
  }

  /**
   * Handle click on overlay background to dismiss it (only after roll is complete)
   */
  onOverlayClick(event: Event): void {
    console.log('Dice overlay clicked:', { hasRolled: this.hasRolled, result: this.overlayState.result, isRolling: this.isRolling });

    // No longer roll dice from overlay click - only dismiss after roll is complete

    // If roll is complete, dismiss the overlay
    if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Handle click on dice to start rolling
   */
  onDiceClick(event: Event): void {
    event.stopPropagation(); // Prevent overlay click
    console.log('Dice clicked:', { isRolling: this.isRolling, hasRolled: this.hasRolled });

    // If dice hasn't been rolled yet, roll it
    if (!this.isRolling && !this.hasRolled) {
      console.log('Starting dice roll...');
      this.startDiceRoll();
    }
    // If roll is complete, dismiss the overlay (clicking dice after roll dismisses)
    else if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay from dice click...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Reset dice animation state
   */
  private resetDiceState(): void {
    this.isRolling = false;
    this.hasRolled = false;
    this.diceTransform = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';
  }

  /**
   * Start the dice rolling animation
   */
  private startDiceRoll(): void {
    this.isRolling = true;

    // Execute the actual dice roll in the service
    // The result will be received via the subscription and finishDiceRoll() will be called
    this.diceOverlayService.executeDiceRoll();
  }

  /**
   * Finish the dice roll and show result
   */
  private finishDiceRoll(): void {
    this.isRolling = false;
    this.hasRolled = true;

    console.log('Dice roll finished:', {
      result: this.overlayState.result,
      hasRolled: this.hasRolled,
      isRolling: this.isRolling,
      overlayState: this.overlayState
    });

    // Force change detection
    setTimeout(() => {
      console.log('After timeout - overlayState:', this.overlayState);
    }, 100);
  }

  /**
   * Get dice rotation for a specific number (simplified for D20)
   */
  private getDiceRotationForNumber(number: number): string {
    // Simplified rotation mapping for D20 faces
    const rotations: { [key: number]: string } = {
      1: 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)',
      2: 'rotateX(90deg) rotateY(0deg) rotateZ(0deg)',
      3: 'rotateX(180deg) rotateY(0deg) rotateZ(0deg)',
      4: 'rotateX(270deg) rotateY(0deg) rotateZ(0deg)',
      5: 'rotateX(0deg) rotateY(90deg) rotateZ(0deg)',
      6: 'rotateX(0deg) rotateY(180deg) rotateZ(0deg)',
      7: 'rotateX(0deg) rotateY(270deg) rotateZ(0deg)',
      8: 'rotateX(90deg) rotateY(90deg) rotateZ(0deg)',
      9: 'rotateX(90deg) rotateY(180deg) rotateZ(0deg)',
      10: 'rotateX(90deg) rotateY(270deg) rotateZ(0deg)',
      11: 'rotateX(180deg) rotateY(90deg) rotateZ(0deg)',
      12: 'rotateX(180deg) rotateY(180deg) rotateZ(0deg)',
      13: 'rotateX(180deg) rotateY(270deg) rotateZ(0deg)',
      14: 'rotateX(270deg) rotateY(90deg) rotateZ(0deg)',
      15: 'rotateX(270deg) rotateY(180deg) rotateZ(0deg)',
      16: 'rotateX(270deg) rotateY(270deg) rotateZ(0deg)',
      17: 'rotateX(45deg) rotateY(45deg) rotateZ(45deg)',
      18: 'rotateX(135deg) rotateY(45deg) rotateZ(45deg)',
      19: 'rotateX(225deg) rotateY(45deg) rotateZ(45deg)',
      20: 'rotateX(315deg) rotateY(45deg) rotateZ(45deg)'
    };

    return rotations[number] || 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';
  }
}
