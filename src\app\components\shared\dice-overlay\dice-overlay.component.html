<!-- BG3-Style Dice Roll Overlay -->
<div class="dice-overlay" *ngIf="overlayState.isVisible" (click)="onOverlayClick($event)">
  <div class="dice-frame">
    <!-- Ornate Border -->
    <div class="frame-border">
      <div class="frame-corner top-left"></div>
      <div class="frame-corner top-right"></div>
      <div class="frame-corner bottom-left"></div>
      <div class="frame-corner bottom-right"></div>
      <div class="frame-edge top"></div>
      <div class="frame-edge bottom"></div>
      <div class="frame-edge left"></div>
      <div class="frame-edge right"></div>
    </div>

    <!-- Content Area -->
    <div class="dice-content">
      <!-- Header with DC -->
      <div class="dice-header">
        <div class="difficulty-label">DIFFICULTY CLASS</div>
        <div class="dc-number">{{overlayState.dc}}</div>
      </div>

      <!-- Dice Area -->
      <div class="dice-area">
        <div class="dice-container" [class.rolling]="isRolling" [class.rolled]="hasRolled">
          <div class="dice-3d"
               [class.rolling]="isRolling"
               [attr.data-face]="hasRolled && overlayState.result?.roll ? overlayState.result.roll : null"
               (click)="onDiceClick($event)">
            <!-- 20 Triangular Faces for D20 -->
            <figure class="face face-1"></figure>
            <figure class="face face-2"></figure>
            <figure class="face face-3"></figure>
            <figure class="face face-4"></figure>
            <figure class="face face-5"></figure>
            <figure class="face face-6"></figure>
            <figure class="face face-7"></figure>
            <figure class="face face-8"></figure>
            <figure class="face face-9"></figure>
            <figure class="face face-10"></figure>
            <figure class="face face-11"></figure>
            <figure class="face face-12"></figure>
            <figure class="face face-13"></figure>
            <figure class="face face-14"></figure>
            <figure class="face face-15"></figure>
            <figure class="face face-16"></figure>
            <figure class="face face-17"></figure>
            <figure class="face face-18"></figure>
            <figure class="face face-19"></figure>
            <figure class="face face-20"></figure>
          </div>
        </div>

        <!-- Critical Success/Failure Text Overlays -->
        <div class="critical-overlay" *ngIf="hasRolled && overlayState.result">
          <div class="critical-text critical-success" *ngIf="overlayState.result.roll === 20">
            CRITICAL SUCCESS
          </div>
          <div class="critical-text critical-failure" *ngIf="overlayState.result.roll === 1">
            CRITICAL FAILURE
          </div>
        </div>

        <!-- Click to Roll Label -->
        <div class="dice-label" *ngIf="!hasRolled && !isRolling">
          Click dice to roll
        </div>
      </div>

      <!-- Result Display -->
      <div class="dice-action">
        <div class="result-display" *ngIf="hasRolled && overlayState.result">
          <div class="outcome"
               [class.success]="overlayState.result.success"
               [class.failure]="!overlayState.result.success">
            {{overlayState.result.success ? 'SUCCESS' : 'FAILURE'}}
          </div>
          <div class="continue-prompt">Click anywhere to continue</div>
        </div>
      </div>
    </div>
  </div>
</div>
