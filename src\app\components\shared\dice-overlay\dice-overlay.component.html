<!-- BG3-Style Dice Roll Overlay -->
<div class="dice-overlay" *ngIf="overlayState.isVisible" (click)="onOverlayClick($event)">
  <div class="dice-frame">
    <!-- Ornate Border -->
    <div class="frame-border">
      <div class="frame-corner top-left"></div>
      <div class="frame-corner top-right"></div>
      <div class="frame-corner bottom-left"></div>
      <div class="frame-corner bottom-right"></div>
      <div class="frame-edge top"></div>
      <div class="frame-edge bottom"></div>
      <div class="frame-edge left"></div>
      <div class="frame-edge right"></div>
    </div>

    <!-- Content Area -->
    <div class="dice-content">
      <!-- Header with DC -->
      <div class="dice-header">
        <div class="difficulty-label">DIFFICULTY CLASS</div>
        <div class="dc-number">{{overlayState.dc}}</div>
      </div>

      <!-- Dice Area -->
      <div class="dice-area">
        <div class="dice-container" [class.rolling]="isRolling" [class.rolled]="hasRolled">
          <div class="dice-flat"
               [class.nat-20]="overlayState.result?.roll === 20"
               [class.nat-1]="overlayState.result?.roll === 1"
               [class.rolling]="isRolling"
               (click)="onDiceClick($event)">
            <!-- Show current number or ? when not rolled -->
            <span class="dice-number">
              {{isRolling ? '?' : (hasRolled && overlayState.result?.roll ? overlayState.result.roll : '?')}}
            </span>
          </div>
        </div>

        <!-- Click to Roll Label -->
        <div class="dice-label" *ngIf="!hasRolled && !isRolling">
          Click dice to roll
        </div>
      </div>

      <!-- Result Display -->
      <div class="dice-action">
        <div class="result-display" *ngIf="hasRolled && overlayState.result">
          <div class="outcome"
               [class.success]="overlayState.result.success"
               [class.failure]="!overlayState.result.success">
            {{overlayState.result.success ? 'SUCCESS' : 'FAILURE'}}
          </div>
          <div class="continue-prompt">Click anywhere to continue</div>
        </div>
      </div>
    </div>
  </div>
</div>
