import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DiceChoicePopupService, DiceChoiceState, DiceChoiceOption } from '../../../services/dice-choice-popup.service';

@Component({
  selector: 'app-dice-choice-popup',
  templateUrl: './dice-choice-popup.component.html',
  styleUrls: ['./dice-choice-popup.component.scss']
})
export class DiceChoicePopupComponent implements OnInit, OnDestroy {
  public choiceState: DiceChoiceState = {
    isVisible: false
  };

  // Expose enum to template
  public DiceChoiceOption = DiceChoiceOption;

  private subscription: Subscription = new Subscription();

  constructor(private diceChoicePopupService: DiceChoicePopupService) { }

  ngOnInit(): void {
    // Subscribe to choice state changes
    this.subscription = this.diceChoicePopupService.choiceState$.subscribe(
      state => {
        this.choiceState = state;
      }
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * Handle user choice selection
   * @param choice The choice made by the user
   */
  onChoiceSelected(choice: DiceChoiceOption): void {
    this.diceChoicePopupService.makeChoice(choice);
  }

  /**
   * Handle popup close (if user clicks outside or presses escape)
   */
  onClose(): void {
    this.diceChoicePopupService.hideChoicePopup();
  }
}
